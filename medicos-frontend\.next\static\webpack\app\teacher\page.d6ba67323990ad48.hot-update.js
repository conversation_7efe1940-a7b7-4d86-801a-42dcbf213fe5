"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - Please log in again. No valid authentication token found.\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper or error object\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            try {\n                // Try to get error message from response body\n                const errorText = await response.text();\n                if (errorText) {\n                    try {\n                        // Try to parse as JSON first\n                        const errorData = JSON.parse(errorText);\n                        // Extract the message from the parsed JSON\n                        if (errorData && errorData.message) {\n                            errorMessage = errorData.message;\n                        } else if (errorData && errorData.error) {\n                            errorMessage = errorData.error;\n                        } else {\n                            errorMessage = errorText;\n                        }\n                    } catch (jsonError) {\n                        // If not JSON, use the text directly\n                        errorMessage = errorText;\n                    }\n                }\n            } catch (parseError) {\n            // Silently handle parse errors\n            }\n            // Provide more specific error messages based on status code if we don't have a message\n            if (!errorMessage || errorMessage === \"Error: \".concat(response.status, \" - \").concat(response.statusText)) {\n                switch(response.status){\n                    case 401:\n                        errorMessage = \"Authentication required - Please log in again.\";\n                        break;\n                    case 403:\n                        errorMessage = \"Access denied - You don't have permission to perform this action.\";\n                        break;\n                    case 404:\n                        errorMessage = \"Resource not found - The requested item could not be found.\";\n                        break;\n                    case 429:\n                        errorMessage = \"Too many requests - Please wait a moment before trying again.\";\n                        break;\n                    case 500:\n                        errorMessage = \"Server error - Please try again later.\";\n                        break;\n                    case 503:\n                        errorMessage = \"Service unavailable - The server is temporarily down.\";\n                        break;\n                    default:\n                        if (response.status >= 400 && response.status < 500) {\n                            errorMessage = \"Invalid request - Please check your input and try again.\";\n                        } else if (response.status >= 500) {\n                            errorMessage = \"Server error - Please try again later.\";\n                        }\n                }\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n        const data = await response.json();\n        console.log(\"Raw API response from createQuestionPaper:\", data);\n        // The backend returns { questionPaper: {...}, college: {...} }\n        // But we need to return it in our expected format\n        if (data.questionPaper) {\n            return {\n                success: true,\n                data: data.questionPaper\n            };\n        } else {\n            // If the response structure is different, return as is\n            return {\n                success: true,\n                data\n            };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            error: \"Network error - Please check your connection and try again.\"\n        };\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @param includeAnswers Whether to include answers in the download\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf', includeAnswers = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        console.log(\"downloadQuestionPaper called with:\", {\n            questionPaperId,\n            format,\n            includeAnswers\n        });\n        // Validate questionPaperId\n        if (!questionPaperId || questionPaperId === 'undefined' || questionPaperId === 'null') {\n            throw new Error(\"Invalid question paper ID: \".concat(questionPaperId));\n        }\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const queryParams = new URLSearchParams({\n            format,\n            ...includeAnswers && {\n                includeAnswers: 'true'\n            }\n        });\n        const downloadUrl = \"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?\").concat(queryParams);\n        console.log(\"Download URL:\", downloadUrl);\n        const response = await fetch(downloadUrl, {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            if (response.status === 401) {\n                errorMessage = \"Authentication required - Please log in again.\";\n            } else if (response.status === 404) {\n                errorMessage = \"Question paper not found.\";\n            } else if (response.status >= 500) {\n                errorMessage = \"Server error - Please try again later.\";\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});