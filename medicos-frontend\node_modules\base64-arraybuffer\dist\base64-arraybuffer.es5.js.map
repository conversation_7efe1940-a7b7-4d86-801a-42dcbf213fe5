{"version": 3, "file": "base64-arraybuffer.es5.js", "sources": ["../../src/index.ts"], "sourcesContent": ["const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\n\nexport const encode = (arraybuffer: ArrayBuffer): string => {\n    let bytes = new Uint8Array(arraybuffer),\n        i,\n        len = bytes.length,\n        base64 = '';\n\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    } else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n\n    return base64;\n};\n\nexport const decode = (base64: string): ArrayBuffer => {\n    let bufferLength = base64.length * 0.75,\n        len = base64.length,\n        i,\n        p = 0,\n        encoded1,\n        encoded2,\n        encoded3,\n        encoded4;\n\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    const arraybuffer = new ArrayBuffer(bufferLength),\n        bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n};\n"], "names": [], "mappings": ";;;;;AAAA,IAAM,KAAK,GAAG,kEAAkE,CAAC;AAEjF;AACA,IAAM,MAAM,GAAG,OAAO,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC5E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACnC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACnC;IAEY,MAAM,GAAG,UAAC,WAAwB;IAC3C,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EACnC,CAAC,EACD,GAAG,GAAG,KAAK,CAAC,MAAM,EAClB,MAAM,GAAG,EAAE,CAAC;IAEhB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;KACtC;IAED,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;QACf,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;KACzD;SAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;QACtB,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;KAC1D;IAED,OAAO,MAAM,CAAC;AAClB,EAAE;IAEW,MAAM,GAAG,UAAC,MAAc;IACjC,IAAI,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,EACnC,GAAG,GAAG,MAAM,CAAC,MAAM,EACnB,CAAC,EACD,CAAC,GAAG,CAAC,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,CAAC;IAEb,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACnC,YAAY,EAAE,CAAC;QACf,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YACnC,YAAY,EAAE,CAAC;SAClB;KACJ;IAED,IAAM,WAAW,GAAG,IAAI,WAAW,CAAC,YAAY,CAAC,EAC7C,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IAExC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QACzB,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5C,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5C,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAE5C,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC;QAC/C,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtD,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,KAAK,QAAQ,GAAG,EAAE,CAAC,CAAC;KACxD;IAED,OAAO,WAAW,CAAC;AACvB;;;;"}